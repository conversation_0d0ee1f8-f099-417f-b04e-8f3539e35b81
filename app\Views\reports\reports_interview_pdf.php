<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Report - <?= esc($position['designation']) ?></title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm 1cm 2.5cm 1cm; /* Extra bottom margin for footer */
            counter-increment: page;

            @bottom-left {
                content: "Page " counter(page) " of " counter(pages) "\A File Code: <?= esc($uniqueCode) ?>";
                font-size: 10px;
                color: #666;
                white-space: pre;
            }

            @bottom-center {
                content: "SelMasta - HR Selection Management System | <?= base_url() ?>";
                font-size: 10px;
                color: #666;
                font-weight: bold;
            }

            @bottom-right {
                content: "Generated: <?= date('M d, Y H:i') ?>";
                font-size: 10px;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            counter-reset: page;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .header h3 {
            margin: 5px 0;
            font-size: 12px;
            color: #888;
        }
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 6px 4px;
            text-align: center;
            vertical-align: middle;
            page-break-inside: avoid; /* Prevent row breaking across pages */
        }

        tr {
            page-break-inside: avoid; /* Keep table rows together */
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 9px;
        }
        
        .rank-col {
            width: 5%;
        }
        
        .name-col {
            width: 20%;
            text-align: left;
        }
        
        .interviewer-col {
            width: <?= count($interviewers) > 0 ? floor(50 / count($interviewers)) : 10 ?>%;
        }
        
        .total-col {
            width: 8%;
        }
        
        .percentage-col {
            width: 8%;
        }
        
        .footer {
            position: relative;
            width: 100%;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding: 10px;
            background-color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            margin-top: 20px;
            page-break-inside: avoid;
        }

        .footer-left {
            text-align: left;
            flex: 1;
        }

        .footer-center {
            text-align: center;
            flex: 2;
        }

        .footer-right {
            text-align: right;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .file-code {
            font-weight: bold;
            font-size: 9px;
            color: #333;
        }
        
        .rank-number {
            font-weight: bold;
            font-size: 11px;
        }
        
        .applicant-name {
            font-weight: bold;
        }
        
        .total-score {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        
        .print-date {
            font-size: 9px;
            color: #999;
            margin-top: 10px;
        }

        .signature-section {
            margin-top: 40px;
            page-break-inside: avoid;
            page-break-before: always; /* Force signature section to new page */
        }

        .page-content {
            min-height: calc(100vh - 120px); /* Reserve space for footer */
            display: flex;
            flex-direction: column;
        }

        .content-area {
            flex-grow: 1;
        }

        .signature-title {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        .signature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 30px;
        }

        .signature-box {
            border: 1px solid #333;
            padding: 20px;
            min-height: 120px;
            position: relative;
        }

        .interviewer-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .interviewer-position {
            font-size: 10px;
            color: #666;
            margin-bottom: 20px;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            margin: 15px 0;
            height: 30px;
        }

        .signature-label {
            font-size: 9px;
            color: #666;
            margin-top: 5px;
        }

        .date-line {
            border-bottom: 1px solid #333;
            margin: 15px 0;
            height: 20px;
            width: 150px;
        }
    </style>
</head>
<body>
    <div class="page-content">
        <div class="content-area">
            <div class="header">
                <h1><?= esc($organization['name']) ?></h1>
                <h2><?= esc($positionGroup['group_name']) ?></h2>
                <h3><?= esc($position['designation']) ?> - Interview Results Report</h3>
                <div class="print-date">Generated on: <?= date('F d, Y \a\t g:i A') ?></div>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th class="rank-col">Rank</th>
                            <th class="name-col">Interviewee Name</th>
                            <?php foreach ($interviewers as $interviewer): ?>
                                <th class="interviewer-col">
                                    <?= esc($interviewer['interviewer_name']) ?><br>
                                    <small><?= esc($interviewer['interviewer_position']) ?></small>
                                </th>
                            <?php endforeach; ?>
                            <th class="total-col">Total Score</th>
                            <th class="total-col">Total Out Of</th>
                            <th class="percentage-col">Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($applicants)): ?>
                            <tr>
                                <td colspan="<?= 5 + count($interviewers) ?>" style="text-align: center; padding: 20px;">
                                    No interviewees found for this position.
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($applicants as $applicant): ?>
                                <tr>
                                    <td class="rank-number"><?= $applicant['rank'] ?></td>
                                    <td class="applicant-name" style="text-align: left;">
                                        <?= esc($applicant['name']) ?>
                                    </td>
                                    <?php foreach ($interviewers as $interviewer): ?>
                                        <td>
                                            <?php if (isset($applicant['interviewer_totals'][$interviewer['id']])): ?>
                                                <?= number_format($applicant['interviewer_totals'][$interviewer['id']]['total'], 1) ?>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                    <?php endforeach; ?>
                                    <td class="total-score">
                                        <?= number_format($applicant['grand_total'], 1) ?>
                                    </td>
                                    <td>
                                        <?= number_format($applicant['max_possible_total'], 1) ?>
                                    </td>
                                    <td>
                                        <?= number_format($applicant['percentage'], 1) ?>%
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Signature Section with its own page -->
    <div class="page-content">
        <div class="content-area">
            <div class="signature-section">
                <div class="signature-title">
                    Interview Panel Signatures
                </div>

                <div class="signature-grid">
                    <?php foreach ($interviewers as $interviewer): ?>
                        <div class="signature-box">
                            <div class="interviewer-name"><?= esc($interviewer['interviewer_name']) ?></div>
                            <div class="interviewer-position"><?= esc($interviewer['interviewer_position']) ?></div>

                            <div class="signature-line"></div>
                            <div class="signature-label">Signature</div>

                            <div class="date-line"></div>
                            <div class="signature-label">Date</div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully rendered
            setTimeout(function() {
                window.print();
            }, 1000);

            // Close window after printing (optional)
            window.onafterprint = function() {
                window.close();
            };
        };
    </script>

    <style>
        /* Print-specific styles */
        @media print {
            body {
                counter-reset: page 1;
                margin: 0;
                padding: 0;
            }

            .page-content {
                page-break-after: always;
                min-height: calc(100vh - 3cm); /* Account for page margins */
                display: flex;
                flex-direction: column;
            }

            .page-content:last-child {
                page-break-after: auto;
            }

            .content-area {
                flex-grow: 1;
            }

            /* Prevent table rows from breaking */
            tr {
                page-break-inside: avoid !important;
            }

            /* Signature section styling */
            .signature-section {
                page-break-before: always;
            }

            .signature-box {
                page-break-inside: avoid !important;
            }

            /* Hide any remaining footer elements since we use @page */
            .footer {
                display: none !important;
            }
        }

        /* Screen display - show JavaScript-calculated page numbers */
        @media screen {
            .page-info {
                display: inline;
            }
        }

        /* Force signature section to new page if needed */
        .signature-section {
            page-break-before: auto;
        }

        /* Ensure signature boxes don't break across pages */
        .signature-box {
            page-break-inside: avoid;
        }
    </style>
</body>
</html>
