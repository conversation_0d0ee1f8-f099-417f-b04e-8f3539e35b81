<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-file-import mr-2"></i>
                        Import Interview Questions
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/questions/' . $positionId) ?>">Questions</a></li>
                        <li class="breadcrumb-item active">Import</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/' . $positionId) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Questions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Import Form Card -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-upload mr-2"></i>
                        Upload CSV File
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/questions/template/' . $positionId) ?>" class="btn btn-info btn-sm">
                            <i class="fas fa-download mr-1"></i>
                            Download Template
                        </a>
                    </div>
                </div>
                <form method="POST" action="<?= base_url('interviews/questions/import/' . $positionId) ?>" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    <div class="card-body">
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger">
                                <h5><i class="icon fas fa-ban"></i> Error!</h5>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <?php if (session()->getFlashdata('import_errors')): ?>
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> Import Warnings!</h5>
                                <ul class="mb-0">
                                    <?php foreach (session()->getFlashdata('import_errors') as $error): ?>
                                        <li><?= esc($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="csv_file">Select CSV File</label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="csv_file" name="csv_file" accept=".csv" required>
                                    <label class="custom-file-label" for="csv_file">Choose CSV file...</label>
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                Please upload a CSV file with the following columns: <strong>question, max score</strong>
                            </small>
                        </div>

                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info-circle"></i> CSV Format Requirements:</h5>
                            <ul class="mb-0">
                                <li><strong>Column 1:</strong> question - The interview question text</li>
                                <li><strong>Column 2:</strong> max score - Maximum score for the question (numeric value)</li>
                                <li>First row should contain column headers: <code>question,max score</code></li>
                                <li>Questions must be unique for this position</li>
                                <li>Max score must be a positive number</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload mr-1"></i>
                            Import Questions
                        </button>
                        <a href="<?= base_url('interviews/questions/' . $positionId) ?>" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>

            <!-- Sample Data Card -->
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-table mr-2"></i>
                        Sample CSV Format
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="thead-light">
                                <tr>
                                    <th>question</th>
                                    <th>max score</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>What are your strengths?</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>Describe your experience with teamwork</td>
                                    <td>15</td>
                                </tr>
                                <tr>
                                    <td>How do you handle pressure?</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>Where do you see yourself in 5 years?</td>
                                    <td>5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted mt-2">
                        <small>
                            <i class="fas fa-lightbulb mr-1"></i>
                            <strong>Tip:</strong> Download the template file to get started with the correct format.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Update file input label when file is selected
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        var fileInput = $('#csv_file')[0];
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('Please select a CSV file to upload.');
            return false;
        }
        
        var fileName = fileInput.files[0].name;
        var fileExtension = fileName.split('.').pop().toLowerCase();
        if (fileExtension !== 'csv') {
            e.preventDefault();
            alert('Please select a CSV file only.');
            return false;
        }
    });
});
</script>
<?= $this->endSection() ?>
