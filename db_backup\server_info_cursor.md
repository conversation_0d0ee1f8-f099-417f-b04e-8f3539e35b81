# SelMasta Five - Server Requirements & Hosting Specifications

## Overview
This document outlines the complete server specifications required to host the SelMasta Five recruitment management system built on CodeIgniter 4.

## **PHP Requirements**

### **PHP Version**
- **Minimum**: PHP 8.1 or higher ✅
- **Recommended**: PHP 8.2 or 8.3 for better performance
- **End of Life**: PHP 8.1 support ends December 31, 2025

### **Required PHP Extensions**
```
✅ mbstring           - Multi-byte string handling (required by CodeIgniter 4)
✅ intl               - Internationalization support (required by CodeIgniter 4)
✅ json               - JSON processing (enabled by default)
✅ mysqlnd            - MySQL native driver (for database connectivity)
✅ libcurl            - HTTP requests and external API calls
✅ fileinfo           - File upload validation and MIME type detection
✅ gd or imagick      - Image processing (GD is default handler)
✅ openssl            - Encryption and security features
✅ zip                - File compression capabilities
✅ pdo_mysql          - PDO MySQL driver (alternative to MySQLi)
```

### **PHP Configuration Settings**
```ini
; File Upload Settings
upload_max_filesize = 32M
post_max_size = 32M
max_file_uploads = 20
max_execution_time = 300
memory_limit = 256M

; Security Settings
expose_php = Off
display_errors = Off                    ; For production
log_errors = On
session.cookie_httponly = On
session.cookie_secure = On              ; If using HTTPS
```

## **Database Requirements**

### **Database Server**
- **MySQL**: 5.7+ or **MariaDB**: 10.3+
- **Database Name**: `selmasta_db`
- **Character Set**: UTF-8 (utf8mb4_general_ci recommended)
- **Port**: 3306 (default)

### **Database Storage**
- **Minimum**: 500MB initial
- **Recommended**: 2GB+ (grows with applicant data, interview records)
- **Backup Space**: Additional 1GB for regular backups

### **Database User Permissions**
```sql
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER 
ON selmasta_db.* TO 'app_user'@'localhost';
```

## **Web Server Requirements**

### **Apache (Recommended)**
- **Version**: Apache 2.4+
- **Required Modules**:
  - mod_rewrite (for clean URLs)
  - mod_headers (for security headers)
  - mod_ssl (for HTTPS)
  - mod_deflate (for compression)

### **Nginx (Alternative)**
- **Version**: Nginx 1.18+
- **Configuration**: URL rewriting rules for CodeIgniter 4
- **SSL/TLS**: Configured for HTTPS

### **Document Root**
- Point web server to `/public` directory
- **NOT** to the project root for security

## **File System & Permissions**

### **Writable Directories (755/775 permissions)**
```
/writable/                              - Main writable directory
/writable/cache/                        - Application cache
/writable/logs/                         - Error and access logs
/writable/session/                      - Session storage
/writable/uploads/                      - Temporary file uploads
/public/uploads/                        - Public file uploads
/public/uploads/org_logo/              - Organization logos
/public/uploads/org_logos/             - Alternative logo directory
/public/uploads/employees/             - Employee photos
```

### **File Upload Features**
The application supports:
- **CSV Import**: Applicant data, interview questions, scoring data
- **Image Upload**: Organization logos (.png, .jpg, .gif)
- **Employee Photos**: ID photos for user profiles
- **PDF Generation**: Interview reports and notifications using TCPDF

## **Server Resources**

### **Minimum Requirements**
- **CPU**: 1 vCore
- **RAM**: 1GB
- **Storage**: 5GB SSD
- **Bandwidth**: 10GB/month
- **Concurrent Users**: 10-20

### **Recommended Specifications**
- **CPU**: 2+ vCores
- **RAM**: 2GB+
- **Storage**: 20GB+ SSD
- **Bandwidth**: 50GB+/month
- **Concurrent Users**: 50-100

### **Enterprise/High-Traffic**
- **CPU**: 4+ vCores
- **RAM**: 4GB+
- **Storage**: 50GB+ SSD
- **Bandwidth**: 100GB+/month
- **Load Balancer**: For multiple instances

## **Dependencies & Libraries**

### **Composer Dependencies (Auto-installed)**
```json
{
    "require": {
        "php": "^8.1",
        "codeigniter4/framework": "^4.0",
        "tecnickcom/tcpdf": "^6.9",
        "bacon/bacon-qr-code": "^3.0",
        "chillerlan/php-qrcode": "^5.0",
        "endroid/qr-code": "^6.0",
        "predis/predis": "^3.0",
        "myclabs/deep-copy": "^1.13",
        "psr/container": "^2.0",
        "symfony/deprecation-contracts": "^3.5"
    }
}
```

### **Third-Party Libraries**
- **FPDF**: PDF generation (included in app/ThirdParty/)
- **AdminLTE**: Admin dashboard theme
- **Bootstrap**: Frontend framework
- **jQuery**: JavaScript library
- **DataTables**: Table management

## **SSL & Security Requirements**

### **SSL Certificate**
- **Required**: SSL/TLS certificate for production
- **Options**: 
  - Let's Encrypt (free, auto-renewal)
  - Commercial SSL certificate
  - Wildcard SSL for subdomains

### **Security Headers**
```apache
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
Header always set Content-Security-Policy "default-src 'self'"
```

### **Firewall Configuration**
```
Port 22   - SSH (restrict to admin IPs)
Port 80   - HTTP (redirect to HTTPS)
Port 443  - HTTPS
Port 3306 - MySQL (localhost only)
```

## **Environment Configuration**

### **Required Environment Variables (.env)**
```env
#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------
CI_ENVIRONMENT = production

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------
app.baseURL = 'https://yourdomain.com'
app.indexPage = ''

#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------
database.default.hostname = localhost
database.default.database = selmasta_db
database.default.username = your_db_user
database.default.password = your_secure_password
database.default.DBDriver = MySQLi
database.default.DBPrefix = 
database.default.port = 3306

#--------------------------------------------------------------------
# SECURITY
#--------------------------------------------------------------------
encryption.key = your-32-character-secret-key
```

## **Performance Optimization**

### **Caching Strategy**
- **File Cache**: Default CodeIgniter 4 caching
- **Redis**: For session storage and caching (optional)
- **Browser Cache**: Configure expires headers
- **CDN**: For static assets (optional)

### **Database Optimization**
- **Indexes**: Ensure proper indexing on frequently queried columns
- **Query Optimization**: Monitor slow query log
- **Connection Pooling**: For high-traffic scenarios

## **Backup Strategy**

### **Database Backups**
```bash
# Daily automated backup
mysqldump -u username -p selmasta_db > backup_$(date +%Y%m%d).sql

# Keep 30 days of backups
find /backup/path -name "backup_*.sql" -mtime +30 -delete
```

### **File Backups**
- **Application Files**: Weekly full backup
- **Upload Directory**: Daily incremental backup
- **Configuration Files**: Include .env and virtual host configs

## **Monitoring & Maintenance**

### **Log Monitoring**
- **Application Logs**: `/writable/logs/`
- **Web Server Logs**: Access and error logs
- **Database Logs**: MySQL error and slow query logs
- **System Logs**: Server resource usage

### **Health Checks**
- **Uptime Monitoring**: External service monitoring
- **Database Connectivity**: Regular connection tests
- **Disk Space**: Monitor storage usage
- **SSL Certificate**: Expiration monitoring

## **Deployment Checklist**

### **Pre-Deployment**
- [ ] Server meets minimum PHP 8.1+ requirements
- [ ] All required PHP extensions installed
- [ ] MySQL/MariaDB database created
- [ ] Web server configured with document root pointing to `/public`
- [ ] SSL certificate installed and configured
- [ ] Firewall rules configured

### **Application Deployment**
- [ ] Application files uploaded to server
- [ ] Composer dependencies installed (`composer install --no-dev`)
- [ ] File permissions set correctly on writable directories
- [ ] Environment variables configured in `.env` file
- [ ] Database imported from `db_backup/interview_tables_structure.sql`
- [ ] URL rewriting tested and working

### **Post-Deployment**
- [ ] Application accessible via web browser
- [ ] Database connectivity verified
- [ ] File upload functionality tested
- [ ] PDF generation tested
- [ ] SSL certificate working properly
- [ ] Log files being created properly
- [ ] Backup strategy implemented
- [ ] Monitoring tools configured

## **Support & Maintenance**

### **Regular Tasks**
- **Weekly**: Review application and server logs
- **Monthly**: Update Composer dependencies
- **Quarterly**: Update PHP and server software
- **Annually**: Review and update SSL certificates

### **Emergency Contacts**
- **Application Developer**: [Contact Information]
- **Server Administrator**: [Contact Information]
- **Database Administrator**: [Contact Information]

---

**Document Created**: $(date)
**Application**: SelMasta Five v1.0
**Framework**: CodeIgniter 4
**Last Updated**: $(date) 