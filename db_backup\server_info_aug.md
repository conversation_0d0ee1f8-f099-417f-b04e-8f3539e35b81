# SelMasta Five - Server Hosting Requirements

This document outlines the complete server specifications needed to host the SelMasta Five CodeIgniter 4 application.

## **Server Requirements**

### **1. PHP Requirements**
- **PHP Version**: 8.1 or higher (required)
- **Recommended**: PHP 8.2 or 8.3 for better performance and security

### **2. Required PHP Extensions**
- **intl** (Internationalization extension)
- **mbstring** (Multi-byte string handling)
- **json** (enabled by default - don't disable)
- **fileinfo** (for file type detection and uploads)
- **gd** or **imagick** (for image processing)
- **curl** (for HTTP requests)
- **openssl** (for secure connections)
- **zip** (for file compression/decompression)

### **3. Database Requirements**
- **MySQL** 5.7+ or **MariaDB** 10.3+
- **Database Name**: `selmasta_db`
- **Port**: 3306 (default)
- **MySQLi** PHP extension enabled

### **4. Web Server**
- **Apache** 2.4+ with:
  - `mod_rewrite` enabled (required for URL rewriting)
  - `mod_headers` enabled
  - `.htaccess` support enabled
- **Alternative**: Nginx with proper URL rewriting configuration

### **5. File System Permissions**
- **Writable directories** (755 or 775):
  - `writable/` (and all subdirectories)
  - `writable/cache/`
  - `writable/logs/`
  - `writable/session/`
  - `public/uploads/` (for file uploads)
  - `public/uploads/org_logo/`
  - `public/uploads/org_logos/`

### **6. Memory and Performance**
- **PHP Memory Limit**: 256MB minimum (512MB recommended)
- **Upload Limits**:
  - `upload_max_filesize`: 10MB minimum
  - `post_max_size`: 12MB minimum
  - `max_file_uploads`: 20
- **Execution Time**: `max_execution_time`: 300 seconds (for PDF generation)

### **7. Email Configuration (SMTP)**
- **SMTP Server**: Access to mail server (currently configured for `mail.dakoiims.com`)
- **SMTP Port**: 465 (SSL) or 587 (TLS)
- **SMTP Authentication**: Username/password required

### **8. Additional Libraries (via Composer)**
Your app uses these libraries that will be installed automatically:
- **TCPDF** (PDF generation)
- **QR Code libraries** (bacon/bacon-qr-code, chillerlan/php-qrcode, endroid/qr-code)
- **Predis** (Redis client)

### **9. Security Configurations**
- **SSL Certificate** (recommended for production)
- **Firewall** configuration
- **Directory browsing disabled** (handled by .htaccess)
- **Server signature disabled**

### **10. Document Root Configuration**
- Point web server document root to the `public/` folder
- **Important**: Never point to the application root directory

### **11. Environment Configuration**
Create `.env` file with:
```env
CI_ENVIRONMENT = production
app.baseURL = 'https://yourdomain.com'
database.default.hostname = your_db_host
database.default.database = selmasta_db
database.default.username = your_db_user
database.default.password = your_db_password
```

### **12. Recommended Server Specs**
- **CPU**: 2+ cores
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 20GB minimum (SSD recommended)
- **Bandwidth**: Based on expected traffic

### **13. Backup Requirements**
- **Database backup** solution
- **File backup** for uploads and application files
- **Regular backup schedule**

### **14. Monitoring**
- **Error logging** enabled
- **Performance monitoring**
- **Uptime monitoring**

## **Application Features**
This application is a recruitment and selection system with:
- File upload capabilities
- PDF generation
- Email notifications
- QR code generation
- Interview management
- Report generation

Ensure your hosting provider supports all these features for optimal performance.

## **Deployment Notes**
1. Install dependencies using `composer install --no-dev`
2. Set proper file permissions
3. Configure environment variables
4. Import database structure
5. Test all functionality before going live

---
*Generated on: 2025-01-22*
*Application: SelMasta Five - Recruitment and Selection System*
