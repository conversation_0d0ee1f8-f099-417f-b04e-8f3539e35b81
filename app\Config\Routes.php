<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(true);
//$autoRoutesImproved(true);
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.

$routes->get('/', 'Home::index');
$routes->get('dashboard', 'Home::dashboard');
$routes->post('login', 'Home::login');
$routes->get('logout', 'Home::logout');


// Dakoii Routes
$routes->post('dakoii/login', 'Dakoii::login');
$routes->get('dakoii', 'Dakoii::index');
$routes->get('dakoii/logout', 'Dakoii::logout');
$routes->get('dakoii/dashboard', 'Dakoii::dashboard');
$routes->post('dakoii/createOrg', 'Dakoii::createOrg');
$routes->post('dakoii/updateOrg/(:num)', 'Dakoii::updateOrg/$1');
$routes->get('dakoii/deleteOrg/(:num)', 'Dakoii::deleteOrg/$1');
$routes->get('dakoii/viewOrg/(:num)', 'Dakoii::viewOrg/$1');

// New user routes
$routes->post('dakoii/createUser/(:num)', 'Dakoii::createUser/$1');
$routes->post('dakoii/updateUser/(:num)', 'Dakoii::updateUser/$1');
$routes->get('dakoii/deleteUser/(:num)', 'Dakoii::deleteUser/$1');

$routes->get('dopen_org/(:any)', 'Dakoii::open_org/$1');
$routes->get('dlist_org', 'Dakoii::list_org');
$routes->post('daddorg', 'Dakoii::addorg');
$routes->post('deditorg', 'Dakoii::editorg');
$routes->post('dadduser', 'Dakoii::adduser');
$routes->post('daddadmin', 'Dakoii::create_admin');
$routes->get('dakoii', 'Dakoii::index');

// Add this new route for the Dakoii dashboard
//$routes->get('dakoii/dashboard', 'Dakoii::dashboard');

// Positions Routes
$routes->group('positions', ['namespace' => 'App\Controllers'], function($routes) {
    $routes->get('/', 'Positions::index');
    $routes->get('new', 'Positions::new');
    $routes->post('create', 'Positions::create');
    $routes->get('show/(:num)', 'Positions::show/$1');
    $routes->get('edit/(:num)', 'Positions::edit/$1');
    $routes->post('update/(:num)', 'Positions::update/$1');
    $routes->get('delete/(:num)', 'Positions::delete/$1');
    // Position-specific routes
    $routes->get('newPosition/(:num)', 'Positions::newPosition/$1');
    $routes->post('createPosition', 'Positions::createPosition');
    $routes->get('editPosition/(:num)', 'Positions::editPosition/$1');
    $routes->post('updatePosition/(:num)', 'Positions::updatePosition/$1');
    $routes->get('deletePosition/(:num)', 'Positions::deletePosition/$1');
});

$routes->group('applicants', ['filter' => 'auth'], function ($routes) {
    $routes->get('/', 'Applicants::positionGroups');
    $routes->get('positionGroups', 'Applicants::positionGroups');
    $routes->get('viewPositions/(:num)', 'Applicants::viewPositions/$1');
    $routes->get('list/(:num)', 'Applicants::listByPosition/$1');
    $routes->get('create/(:num)', 'Applicants::create/$1');
    $routes->post('store/(:num)', 'Applicants::store/$1');
    $routes->get('edit/(:num)', 'Applicants::edit/$1');
    $routes->post('update/(:num)', 'Applicants::update/$1');
    $routes->get('delete/(:num)', 'Applicants::delete/$1');
    $routes->get('import/(:num)', 'Applicants::import/$1');
    $routes->post('processImport/(:num)', 'Applicants::processImport/$1');
    $routes->get('applicant_import/(:num)', 'Applicants::applicant_import/$1');
    $routes->post('processApplicantImport/(:num)', 'Applicants::processApplicantImport/$1');
    $routes->post('eliminate/(:num)', 'Applicants::eliminate/$1');
});

// Ratings routes
$routes->group('ratings', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('/', 'Ratings::index');
    $routes->get('viewPositions/(:num)', 'Ratings::viewPositions/$1');
    $routes->get('viewApplicants/(:num)', 'Ratings::viewApplicants/$1');
    $routes->get('rateApplicant/(:num)', 'Ratings::rateApplicant/$1');
    $routes->post('updateApplicantRating/(:num)', 'Ratings::updateApplicantRating/$1');
    $routes->post('eliminate/(:num)', 'Ratings::eliminate/$1');
    $routes->post('undoElimination/(:num)', 'Ratings::undoElimination/$1');
    $routes->post('rateAges', 'Ratings::rateAges');
});

// Interview routes
$routes->group('interviews', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('/', 'Interview::index');
    $routes->get('status', 'Interview::interviewerStatus');
    $routes->get('open/(:num)', 'Interview::open/$1');

    // Interview Questions routes
    $routes->get('questions/(:num)', 'Interview::questions/$1');
    $routes->get('questions/create/(:num)', 'Interview::createQuestion/$1');
    $routes->post('questions/store/(:num)', 'Interview::storeQuestion/$1');
    $routes->get('questions/edit/(:num)', 'Interview::editQuestion/$1');
    $routes->post('questions/update/(:num)', 'Interview::updateQuestion/$1');
    $routes->post('questions/delete/(:num)', 'Interview::deleteQuestion/$1');

    // Interview Questions Import routes
    $routes->get('questions/import/(:num)', 'Interview::importQuestions/$1');
    $routes->post('questions/import/(:num)', 'Interview::processImportQuestions/$1');
    $routes->get('questions/template/(:num)', 'Interview::downloadTemplate/$1');

    // Interviewers routes
    $routes->get('interviewers/(:num)', 'Interview::interviewers/$1');
    $routes->get('interviewers/create/(:num)', 'Interview::createInterviewer/$1');
    $routes->post('interviewers/store/(:num)', 'Interview::storeInterviewer/$1');
    $routes->get('interviewers/edit/(:num)', 'Interview::editInterviewer/$1');
    $routes->post('interviewers/update/(:num)', 'Interview::updateInterviewer/$1');
    $routes->post('interviewers/delete/(:num)', 'Interview::deleteInterviewer/$1');

    // Interviewer Import routes
    $routes->get('interviewers/import/(:num)', 'Interview::importInterviewers/$1');
    $routes->post('interviewers/import/(:num)', 'Interview::processImportInterviewers/$1');
    $routes->get('interviewers/template/(:num)', 'Interview::downloadInterviewersTemplate/$1');

    // Interview Data routes
    $routes->get('data/view/(:num)', 'Interview::viewInterviewData/$1');
    $routes->post('data/store/(:num)', 'Interview::storeInterviewData/$1');
    $routes->post('data/update/(:num)', 'Interview::updateInterviewData/$1');

    // Interview Data Import routes
    $routes->get('data/import/(:num)', 'Interview::importInterviewData/$1');
    $routes->post('data/import/(:num)', 'Interview::processImportInterviewData/$1');
    $routes->get('data/template/(:num)', 'Interview::downloadInterviewDataTemplate/$1');
});

// AI routes
$routes->group('ai', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->post('analyze', 'AIController::aiAnalysis');
});

$routes->group('reports', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('/', 'Reports::index');
    $routes->get('viewPositions/(:num)', 'Reports::viewPositions/$1');
    $routes->get('applicantProfiles/(:num)', 'Reports::applicantProfiles/$1');
    $routes->get('listEliminatedApplicants/(:num)', 'Reports::listEliminatedApplicants/$1');
    $routes->get('listEliminatedApplicants/(:num)/(:num)', 'Reports::listEliminatedApplicants/$1/$2');
    $routes->get('listApplicantsByRank/(:num)', 'Reports::listApplicantsByRank/$1');
    $routes->get('listApplicantsByRank/(:num)/(:num)', 'Reports::listApplicantsByRank/$1/$2');
    $routes->get('preSelectionReport', 'Reports::preSelectionReport');
    $routes->post('saveRemarks', 'Reports::saveRemarks');
    $routes->get('analysisReport/(:num)/(:num)', 'Reports::analysisReport/$1/$2');
    $routes->get('viewApplicantAnalysis/(:num)', 'Reports::viewApplicantAnalysis/$1');
    $routes->get('interviewList/(:num)', 'Reports::interviewList/$1');
    $routes->get('interviewSchedule', 'Reports::interviewSchedule');
    // Add bulk action routes
    $routes->post('bulk_shortlist', 'Reports::bulk_shortlist');
    $routes->post('bulk_eliminate', 'Reports::bulk_eliminate');
    $routes->post('bulk_withdraw', 'Reports::bulk_withdraw');
});

// Interview Reports routes
$routes->group('interview-reports', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('index/(:num)', 'InterviewReports::index/$1');
    $routes->get('view/(:num)', 'InterviewReports::view/$1');
    $routes->get('cumulative/(:num)', 'InterviewReports::cumulative/$1');
    $routes->get('pdf/(:num)', 'InterviewReports::generatePDF/$1');
});

// Settings routes
$routes->get('settings', 'Settings::index');
$routes->post('settings_advertisement', 'Settings::settings_advertisement');
$routes->post('settings_logo', 'Settings::settings_logo');
$routes->post('settings_report', 'Settings::settings_report');
$routes->get('settings/interviews', 'Settings::interviews');
$routes->post('settings/save_interview_settings', 'Settings::save_interview_settings');
$routes->get('settings/pre_selection_report', 'Settings::pre_selection_report');
$routes->post('settings/save_pre_selection_report', 'Settings::save_pre_selection_report');
$routes->post('settings/save_signatures', 'Settings::save_signatures');

$routes->get('applicants/search', 'Applicants::search');

$routes->get('reports/applicantProfiles/(:num)', 'Applicants::applicantProfiles/$1');

$routes->get('applicants/generateTotal/(:num)', 'Applicants::generateTotal/$1');

$routes->get('reports/preSelectionReport', 'Reports::preSelectionReport');

$routes->post('applicants/shortlist/(:num)', 'Applicants::shortlist/$1');
$routes->post('applicants/eliminate/(:num)', 'Applicants::eliminate/$1');
$routes->post('applicants/withdraw/(:num)', 'Applicants::withdraw/$1');

$routes->post('reports/saveRemarks', 'Reports::saveRemarks');

// Add these routes inside the routes configuration
$routes->get('applicants/applicant_import/(:num)', 'Applicants::applicant_import/$1');
$routes->post('applicants/applicant_import/(:num)', 'Applicants::applicant_import/$1');
$routes->get('applicants/searchApplicants', 'Applicants::searchApplicants');

// Interview notification routes - specify HTTP methods explicitly
$routes->get('interview/sendNotifications', 'InterviewController::sendNotifications');
$routes->post('interview/sendNotifications', 'InterviewController::sendNotifications');
$routes->get('InterviewController/sendNotifications', 'InterviewController::sendNotifications');
$routes->post('InterviewController/sendNotifications', 'InterviewController::sendNotifications');

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
