<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\InterviewDataModel;
use App\Models\InterviewerModel;
use App\Models\InterviewQuestionModel;
use App\Models\ApplicantModel;
use App\Models\DakoiiOrgModel;

class InterviewReports extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $interviewDataModel;
    protected $interviewerModel;
    protected $interviewQuestionModel;
    protected $applicantModel;
    protected $dakoiiOrgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->interviewDataModel = new InterviewDataModel();
        $this->interviewerModel = new InterviewerModel();
        $this->interviewQuestionModel = new InterviewQuestionModel();
        $this->applicantModel = new ApplicantModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }

    /**
     * Display positions in a group for interview reports
     * GET /interview-reports/index/{group_id}
     */
    public function index($groupId = null)
    {
        if (!$groupId) {
            return redirect()->to('reports')->with('error', 'Position Group ID is required.');
        }

        $orgId = session('org_id');

        // Get position group details
        $positionGroup = $this->positionsGroupModel->where('id', $groupId)
            ->where('org_id', $orgId)
            ->first();

        if (!$positionGroup) {
            return redirect()->to('reports')->with('error', 'Position Group not found.');
        }

        // Get positions in this group that are marked for interview
        $positions = $this->positionModel->where('position_group_id', $groupId)
            ->where('org_id', $orgId)
            ->where('for_interview', 1)
            ->where('is_active', 1)
            ->orderBy('position_no', 'ASC')
            ->findAll();

        // For each position, get interview statistics
        foreach ($positions as &$position) {
            // Count shortlisted applicants
            $shortlistedCount = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', $orgId)
                ->where('application_status', 'Shortlisted')
                ->countAllResults();

            // Count questions and interviewers
            $questionsCount = $this->interviewQuestionModel->where('position_id', $position['id'])
                ->where('is_deleted', false)
                ->countAllResults();

            $interviewersCount = $this->interviewerModel->where('position_id', $position['id'])
                ->where('is_deleted', false)
                ->countAllResults();

            // Count interview data entries
            $interviewDataCount = $this->interviewDataModel->where('position_id', $position['id'])
                ->where('is_deleted', false)
                ->countAllResults();

            // Calculate expected total entries
            $expectedEntries = $shortlistedCount * $questionsCount * $interviewersCount;
            $completionPercentage = $expectedEntries > 0 ? ($interviewDataCount / $expectedEntries) * 100 : 0;

            $position['shortlisted_count'] = $shortlistedCount;
            $position['questions_count'] = $questionsCount;
            $position['interviewers_count'] = $interviewersCount;
            $position['interview_data_count'] = $interviewDataCount;
            $position['expected_entries'] = $expectedEntries;
            $position['completion_percentage'] = $completionPercentage;
            $position['is_ready'] = $questionsCount > 0 && $interviewersCount > 0 && $shortlistedCount > 0;
        }

        $data['title'] = 'Interview Reports - ' . $positionGroup['group_name'];
        $data['menu'] = 'reports';
        $data['positionGroup'] = $positionGroup;
        $data['positions'] = $positions;

        return view('reports/reports_interview_index', $data);
    }

    /**
     * Display interview report for a specific position
     * GET /interview-reports/view/{position_id}
     */
    public function view($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('reports')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->where('for_interview', 1)
            ->where('is_active', 1)
            ->first();

        if (!$position) {
            return redirect()->to('reports')->with('error', 'Position not found or not available for interview.');
        }

        // Get position group details
        $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);

        // Get shortlisted applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get interview questions for this position
        $questions = $this->interviewQuestionModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('question_no', 'ASC')
            ->findAll();

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('interviewer_name', 'ASC')
            ->findAll();

        // Get all interview data for this position
        $interviewData = $this->interviewDataModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->findAll();

        // Organize interview data by applicant and interviewer
        $scores = [];
        foreach ($interviewData as $data) {
            $scores[$data['applicant_id']][$data['interviewer_id']][$data['question_id']] = [
                'score' => $data['score'],
                'comments' => $data['comments']
            ];
        }

        // Calculate totals for each applicant
        $applicantTotals = [];
        $totalPossibleScore = 0;
        
        // Calculate total possible score
        foreach ($questions as $question) {
            $totalPossibleScore += $question['set_score'];
        }

        foreach ($applicants as &$applicant) {
            $applicantId = $applicant['id'];
            $interviewerTotals = [];
            $grandTotal = 0;
            $totalScoresEntered = 0;

            foreach ($interviewers as $interviewer) {
                $interviewerTotal = 0;
                $interviewerScoresCount = 0;

                foreach ($questions as $question) {
                    if (isset($scores[$applicantId][$interviewer['id']][$question['id']])) {
                        $score = $scores[$applicantId][$interviewer['id']][$question['id']]['score'];
                        if ($score !== null && $score !== '') {
                            $interviewerTotal += floatval($score);
                            $interviewerScoresCount++;
                        }
                    }
                }

                $interviewerTotals[$interviewer['id']] = [
                    'total' => $interviewerTotal,
                    'scores_count' => $interviewerScoresCount,
                    'possible_total' => $totalPossibleScore
                ];

                $grandTotal += $interviewerTotal;
                $totalScoresEntered += $interviewerScoresCount;
            }

            $averageScore = count($interviewers) > 0 ? $grandTotal / count($interviewers) : 0;
            $maxPossibleTotal = $totalPossibleScore * count($interviewers);
            $percentage = $maxPossibleTotal > 0 ? ($grandTotal / $maxPossibleTotal) * 100 : 0;

            $applicant['interviewer_totals'] = $interviewerTotals;
            $applicant['grand_total'] = $grandTotal;
            $applicant['average_score'] = $averageScore;
            $applicant['percentage'] = $percentage;
            $applicant['max_possible_total'] = $maxPossibleTotal;
            $applicant['total_scores_entered'] = $totalScoresEntered;
            $applicant['expected_scores'] = count($questions) * count($interviewers);
        }

        // Sort applicants by total score in descending order (highest score first)
        usort($applicants, function($a, $b) {
            return $b['grand_total'] <=> $a['grand_total'];
        });

        // Assign ranks with proper tie handling
        $currentRank = 1;
        $previousScore = null;
        $sameRankCount = 0;

        foreach ($applicants as $index => &$applicant) {
            if ($previousScore !== null && $applicant['grand_total'] < $previousScore) {
                // Score is different from previous, update rank
                $currentRank = $index + 1;
            }

            $applicant['rank'] = $currentRank;
            $previousScore = $applicant['grand_total'];
        }

        $data['title'] = 'Interview Report - ' . $position['designation'];
        $data['menu'] = 'reports';
        $data['position'] = $position;
        $data['positionGroup'] = $positionGroup;
        $data['applicants'] = $applicants;
        $data['questions'] = $questions;
        $data['interviewers'] = $interviewers;
        $data['scores'] = $scores;
        $data['totalPossibleScore'] = $totalPossibleScore;

        return view('reports/reports_interview_view', $data);
    }

    /**
     * Generate PDF for interview report
     * GET /interview-reports/pdf/{position_id}
     */
    public function generatePDF($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('reports')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->where('for_interview', 1)
            ->where('is_active', 1)
            ->first();

        if (!$position) {
            return redirect()->to('reports')->with('error', 'Position not found or not available for interview.');
        }

        // Get organization details
        $orgModel = new \App\Models\DakoiiOrgModel();
        $organization = $orgModel->find($orgId);

        // Get position group details
        $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);

        // Get shortlisted applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get interview questions for this position
        $questions = $this->interviewQuestionModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('question_no', 'ASC')
            ->findAll();

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('interviewer_name', 'ASC')
            ->findAll();

        // Get all interview data for this position
        $interviewData = $this->interviewDataModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->findAll();

        // Organize interview data by applicant and interviewer
        $scores = [];
        foreach ($interviewData as $data) {
            $scores[$data['applicant_id']][$data['interviewer_id']][$data['question_id']] = [
                'score' => $data['score'],
                'comments' => $data['comments']
            ];
        }

        // Calculate totals for each applicant
        $totalPossibleScore = 0;

        // Calculate total possible score
        foreach ($questions as $question) {
            $totalPossibleScore += $question['set_score'];
        }

        foreach ($applicants as &$applicant) {
            $applicantId = $applicant['id'];
            $interviewerTotals = [];
            $grandTotal = 0;

            foreach ($interviewers as $interviewer) {
                $interviewerTotal = 0;

                foreach ($questions as $question) {
                    if (isset($scores[$applicantId][$interviewer['id']][$question['id']])) {
                        $score = $scores[$applicantId][$interviewer['id']][$question['id']]['score'];
                        if ($score !== null && $score !== '') {
                            $interviewerTotal += floatval($score);
                        }
                    }
                }

                $interviewerTotals[$interviewer['id']] = [
                    'total' => $interviewerTotal,
                    'possible_total' => $totalPossibleScore
                ];

                $grandTotal += $interviewerTotal;
            }

            $maxPossibleTotal = $totalPossibleScore * count($interviewers);
            $percentage = $maxPossibleTotal > 0 ? ($grandTotal / $maxPossibleTotal) * 100 : 0;

            $applicant['interviewer_totals'] = $interviewerTotals;
            $applicant['grand_total'] = $grandTotal;
            $applicant['percentage'] = $percentage;
            $applicant['max_possible_total'] = $maxPossibleTotal;
        }

        // Sort applicants by total score in descending order (highest score first)
        usort($applicants, function($a, $b) {
            return $b['grand_total'] <=> $a['grand_total'];
        });

        // Assign ranks with proper tie handling
        $currentRank = 1;
        $previousScore = null;

        foreach ($applicants as $index => &$applicant) {
            if ($previousScore !== null && $applicant['grand_total'] < $previousScore) {
                $currentRank = $index + 1;
            }

            $applicant['rank'] = $currentRank;
            $previousScore = $applicant['grand_total'];
        }

        // Generate unique file code
        $timestamp = date('YmdHis');
        $uniqueCode = 'IR-' . $positionId . '-' . $timestamp . '-' . substr(md5($positionId . $timestamp), 0, 6);

        $data['organization'] = $organization;
        $data['position'] = $position;
        $data['positionGroup'] = $positionGroup;
        $data['applicants'] = $applicants;
        $data['interviewers'] = $interviewers;
        $data['totalPossibleScore'] = $totalPossibleScore;
        $data['uniqueCode'] = $uniqueCode;

        return view('reports/reports_interview_pdf', $data);
    }

    public function cumulative($positionId)
    {
        // Get organization info
        $organization = $this->dakoiiOrgModel->find(session('org_id'));

        // Get position information
        $position = $this->positionModel->find($positionId);
        if (!$position) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Position not found');
        }

        // Get position group information
        $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);

        // Get shortlisted applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('pre_select_status', 'Shortlisted')
            ->where('is_active', 1)
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get interview questions for this position
        $questions = $this->interviewQuestionModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('question_no', 'ASC')
            ->findAll();

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->orderBy('interviewer_name', 'ASC')
            ->findAll();

        // Get all interview data for this position
        $interviewData = $this->interviewDataModel->where('position_id', $positionId)
            ->where('is_deleted', false)
            ->findAll();

        // Organize interview data by applicant and interviewer
        $scores = [];
        foreach ($interviewData as $data) {
            $scores[$data['applicant_id']][$data['interviewer_id']][$data['question_id']] = [
                'score' => $data['score'],
                'comments' => $data['comments']
            ];
        }

        // Calculate total possible interview score
        $totalPossibleInterviewScore = 0;
        foreach ($questions as $question) {
            $totalPossibleInterviewScore += $question['set_score'];
        }

        // Calculate cumulative scores for each applicant
        foreach ($applicants as &$applicant) {
            $applicantId = $applicant['id'];

            // Calculate interview total
            $interviewTotal = 0;
            foreach ($interviewers as $interviewer) {
                foreach ($questions as $question) {
                    if (isset($scores[$applicantId][$interviewer['id']][$question['id']])) {
                        $score = $scores[$applicantId][$interviewer['id']][$question['id']]['score'];
                        if ($score !== null && $score !== '') {
                            $interviewTotal += floatval($score);
                        }
                    }
                }
            }

            // Calculate average interview score
            $averageInterviewScore = count($interviewers) > 0 ? $interviewTotal / count($interviewers) : 0;

            // Calculate rating total (pre-selection scores)
            $ratingTotal = ($applicant['rate_age'] ?? 0) +
                          ($applicant['rate_qualification'] ?? 0) +
                          ($applicant['rate_experience'] ?? 0) +
                          ($applicant['rate_trainings'] ?? 0) +
                          ($applicant['rate_skills_competencies'] ?? 0) +
                          ($applicant['rate_knowledge'] ?? 0) +
                          ($applicant['rate_public_service'] ?? 0) +
                          ($applicant['rate_capability'] ?? 0);

            // Calculate cumulative total (rating + average interview score)
            $cumulativeTotal = $ratingTotal + $averageInterviewScore;

            // Add calculated values to applicant data
            $applicant['interview_total'] = $interviewTotal;
            $applicant['average_interview_score'] = $averageInterviewScore;
            $applicant['rating_total'] = $ratingTotal;
            $applicant['cumulative_total'] = $cumulativeTotal;
            $applicant['max_possible_interview'] = $totalPossibleInterviewScore;
        }

        // Sort applicants by cumulative total in descending order (highest score first)
        usort($applicants, function($a, $b) {
            return $b['cumulative_total'] <=> $a['cumulative_total'];
        });

        // Assign ranks with tied scores handling
        $currentRank = 1;
        for ($i = 0; $i < count($applicants); $i++) {
            if ($i > 0 && $applicants[$i]['cumulative_total'] < $applicants[$i-1]['cumulative_total']) {
                $currentRank = $i + 1;
            }
            $applicants[$i]['rank'] = $currentRank;
        }

        $data = [
            'organization' => $organization,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'applicants' => $applicants,
            'interviewers' => $interviewers,
            'questions' => $questions,
            'totalPossibleInterviewScore' => $totalPossibleInterviewScore
        ];

        return view('reports/reports_interview_cumulative', $data);
    }
}
