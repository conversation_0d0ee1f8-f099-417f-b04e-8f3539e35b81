<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-users mr-2"></i>
                        Interviewers
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item active">Interviewers</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/open/' . $position['id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Interview
                        </a>
                    </div>
                </div>
            </div>

            <!-- Interviewers Card -->
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users mr-2"></i>
                        Interview Panel
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/interviewers/import/' . $positionId) ?>" class="btn btn-info btn-sm mr-2">
                            <i class="fas fa-file-import mr-1"></i>
                            Import Interviewers
                        </a>
                        <a href="<?= base_url('interviews/interviewers/create/' . $positionId) ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-user-plus mr-1"></i>
                            Add Interviewer
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($interviewers)): ?>
                        <div class="alert alert-info m-3">
                            <h5><i class="icon fas fa-info-circle"></i> No Interviewers Added</h5>
                            <p>No interviewers have been assigned to this position yet. Click "Add Interviewer" to add members to the interview panel.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="interviewersTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="8%">#</th>
                                        <th width="35%">Interviewer Name</th>
                                        <th width="35%">Position/Title</th>
                                        <th width="12%" class="text-center">Added On</th>
                                        <th width="10%" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($interviewers as $index => $interviewer): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-primary badge-lg">
                                                    <?= $index + 1 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong><?= esc($interviewer['interviewer_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <?= esc($interviewer['interviewer_position']) ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($interviewer['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('interviews/interviewers/edit/' . $interviewer['id']) ?>" 
                                                       class="btn btn-warning btn-sm" data-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" 
                                                            data-toggle="modal" 
                                                            data-target="#deleteModal<?= $interviewer['id'] ?>"
                                                            data-toggle="tooltip" title="Remove">
                                                        <i class="fas fa-user-minus"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?= $interviewer['id'] ?>" tabindex="-1" role="dialog">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger">
                                                        <h5 class="modal-title text-white">
                                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                                            Confirm Remove
                                                        </h5>
                                                        <button type="button" class="close text-white" data-dismiss="modal">
                                                            <span>&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to remove this interviewer from the panel?</p>
                                                        <div class="alert alert-light">
                                                            <strong>Interviewer:</strong> <?= esc($interviewer['interviewer_name']) ?><br>
                                                            <strong>Position:</strong> <?= esc($interviewer['interviewer_position']) ?>
                                                        </div>
                                                        <p class="text-danger"><small><strong>Warning:</strong> This action cannot be undone.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                        <form method="POST" action="<?= base_url('interviews/interviewers/delete/' . $interviewer['id']) ?>" style="display: inline;">
                                                            <?= csrf_field() ?>
                                                            <button type="submit" class="btn btn-danger">
                                                                <i class="fas fa-user-minus mr-1"></i>
                                                                Remove Interviewer
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Interview Panel Summary -->
            <div class="card card-info card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Panel Summary
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Interviewers</span>
                                    <span class="info-box-number"><?= count($interviewers) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Panel Status</span>
                                    <span class="info-box-number">
                                        <?= count($interviewers) > 0 ? 'Ready' : 'Pending' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interview Ready</span>
                                    <span class="info-box-number">
                                        <?= count($interviewers) >= 2 ? 'Yes' : 'No' ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (count($interviewers) > 0): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-info-circle mr-2"></i>Interview Panel Guidelines:</h6>
                                    <ul class="mb-0">
                                        <li>Ensure all panel members are available for the scheduled interview dates</li>
                                        <li>Brief all interviewers on the evaluation criteria and scoring system</li>
                                        <li>Assign roles (e.g., lead interviewer, technical evaluator, HR representative)</li>
                                        <li>Prepare interview questions and evaluation forms in advance</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>Panel Setup Required:</h6>
                                    <p class="mb-0">Please add at least 2-3 interviewers to form a complete interview panel before conducting interviews.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#interviewersTable').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false,
        "order": [[ 0, "asc" ]], // Sort by number
        "columnDefs": [
            { "orderable": false, "targets": [4] } // Disable sorting for actions column
        ]
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?= $this->endSection() ?>
