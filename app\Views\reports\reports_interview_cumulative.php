<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-calculator mr-2"></i>
                        Cumulative Interview Report
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interview-reports/index/' . $positionGroup['id']) ?>">Interview Reports</a></li>
                        <li class="breadcrumb-item active">Cumulative Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position Information
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interview-reports/index/' . $positionGroup['id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Interview Reports
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Organization:</strong></td>
                                    <td><?= esc($organization['name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Position Group:</strong></td>
                                    <td><?= esc($positionGroup['group_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Position No:</strong></td>
                                    <td><?= esc($position['position_no']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Designation:</strong></td>
                                    <td><?= esc($position['designation']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Classification:</strong></td>
                                    <td><?= esc($position['classification']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Award:</strong></td>
                                    <td><?= esc($position['award']) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cumulative Results Table -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-table mr-2"></i>
                        Cumulative Interview Results
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-success mr-2">
                            <?= count($applicants) ?> Applicants
                        </span>
                        <button onclick="printTableToPDF()" class="btn btn-danger btn-sm">
                            <i class="fas fa-file-pdf mr-1"></i> Print to PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($applicants)): ?>
                        <div class="alert alert-warning">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> No Applicants</h5>
                            <p>There are no shortlisted applicants for this position.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive" id="cumulative-results-table">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th width="8%" class="text-center">Rank</th>
                                        <th width="25%">Applicant Name</th>
                                        <th width="8%" class="text-center">Age</th>
                                        <th width="10%" class="text-center">Rating Score</th>
                                        <th width="12%" class="text-center">Interview Score</th>
                                        <th width="12%" class="text-center">Cumulative Score</th>
                                        <th width="10%" class="text-center">Percentage</th>
                                        <th width="15%" class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applicants as $applicant): ?>
                                        <?php 
                                            $maxPossibleRating = 100; // Assuming max rating is 100
                                            $maxPossibleTotal = $maxPossibleRating + $totalPossibleInterviewScore;
                                            $percentage = $maxPossibleTotal > 0 ? ($applicant['cumulative_total'] / $maxPossibleTotal) * 100 : 0;
                                        ?>
                                        <tr>
                                            <td class="text-center align-middle">
                                                <?= $applicant['rank'] ?>
                                            </td>
                                            <td class="align-middle">
                                                <strong><?= esc($applicant['name']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($applicant['sex']) ?></small>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?= esc($applicant['age']) ?>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?= number_format($applicant['rating_total'], 1) ?>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?= number_format($applicant['average_interview_score'], 1) ?>
                                                <br>
                                                <small class="text-muted">out of <?= $totalPossibleInterviewScore ?></small>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?= number_format($applicant['cumulative_total'], 1) ?>
                                            </td>
                                            <td class="text-center align-middle">
                                                <?= number_format($percentage, 1) ?>%
                                            </td>
                                            <td class="text-center align-middle">
                                                <?php if ($applicant['rank'] <= 3): ?>
                                                    Top Candidate
                                                <?php elseif ($percentage >= 70): ?>
                                                    Excellent
                                                <?php elseif ($percentage >= 60): ?>
                                                    Good
                                                <?php elseif ($percentage >= 50): ?>
                                                    Average
                                                <?php else: ?>
                                                    Below Average
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="info-box bg-primary">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Applicants</span>
                                        <span class="info-box-number"><?= count($applicants) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-trophy"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Highest Score</span>
                                        <span class="info-box-number"><?= !empty($applicants) ? number_format($applicants[0]['cumulative_total'], 1) : '0' ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Average Score</span>
                                        <span class="info-box-number">
                                            <?= !empty($applicants) ? number_format(array_sum(array_column($applicants, 'cumulative_total')) / count($applicants), 1) : '0' ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Max Possible</span>
                                        <span class="info-box-number"><?= $maxPossibleRating + $totalPossibleInterviewScore ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize DataTable for results
    $('#cumulative-results-table table').DataTable({
        "responsive": true,
        "lengthChange": false,
        "autoWidth": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "paging": false, // Show all results
        "order": [[ 0, "asc" ]], // Sort by rank ascending
        "columnDefs": [
            { "orderable": false, "targets": [0] } // Disable sorting for rank column to maintain ranking
        ]
    });
});

// Print functionality
function printTableToPDF() {
    // Get the table element and clone it
    var tableElement = document.querySelector('#cumulative-results-table table');
    var clonedTable = tableElement.cloneNode(true);

    // Create a temporary container for the print content
    var printContents = clonedTable.outerHTML;
    
    // Create a new window for printing
    var printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Cumulative Interview Report - <?= esc($position['designation']) ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .text-center { text-align: center; }
                h1 { text-align: center; margin-bottom: 30px; }
                .header-info { margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>Cumulative Interview Report</h1>
            <div class="header-info">
                <p><strong>Organization:</strong> <?= esc($organization['name']) ?></p>
                <p><strong>Position:</strong> <?= esc($position['designation']) ?> (<?= esc($position['position_no']) ?>)</p>
                <p><strong>Position Group:</strong> <?= esc($positionGroup['group_name']) ?></p>
                <p><strong>Generated on:</strong> <?= date('F j, Y g:i A') ?></p>
            </div>
            ${printContents}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}
</script>
<?= $this->endSection() ?>
